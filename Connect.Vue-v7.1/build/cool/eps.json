[{"prefix": "/admin/base/comm", "name": "", "api": [{"method": "post", "path": "/personUpdate"}, {"method": "get", "path": "/uploadMode"}, {"method": "get", "path": "/permmenu"}, {"method": "get", "path": "/person"}, {"method": "post", "path": "/upload"}, {"method": "post", "path": "/logout"}]}, {"prefix": "/admin/base/common/chatgpt", "name": "ChatgptSessionEntity", "api": [{"method": "post", "path": "/generateImageFromText"}, {"method": "post", "path": "/gptsAuthUpdate"}, {"method": "post", "path": "/gptsAuthDelete"}, {"method": "post", "path": "/destroySession"}, {"method": "post", "path": "/updateSubject"}, {"method": "post", "path": "/deleteSession"}, {"method": "post", "path": "/aiVenderModel"}, {"method": "post", "path": "/gptsAuthPage"}, {"method": "post", "path": "/gptsAuthAdd"}, {"method": "post", "path": "/voiceToText"}, {"method": "post", "path": "/textToVoice"}, {"method": "post", "path": "/unbindTag"}, {"method": "post", "path": "/sessions"}, {"method": "post", "path": "/aiVender"}, {"method": "get", "path": "/chatgpt4"}, {"method": "post", "path": "/bindTag"}, {"method": "post", "path": "/details"}, {"method": "post", "path": "/delTag"}, {"method": "get", "path": "/users"}, {"method": "get", "path": "/tags"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/tag"}, {"method": "get", "path": "/session/:sessionid"}]}, {"prefix": "/admin/base/common/douyin", "name": "DouyinUrlEntity", "api": [{"method": "post", "path": "/douyinUrlDownload"}, {"method": "post", "path": "/douyinUrlParse"}, {"method": "post", "path": "/tagUserCreate"}, {"method": "post", "path": "/tagUserDelete"}, {"method": "post", "path": "/accountCookie"}, {"method": "post", "path": "/accountCreate"}, {"method": "post", "path": "/accountDelete"}, {"method": "post", "path": "/accountSwitch"}, {"method": "post", "path": "/accountStatus"}, {"method": "post", "path": "/tagUserList"}, {"method": "post", "path": "/checkQrCode"}, {"method": "post", "path": "/loginQrCode"}, {"method": "post", "path": "/tagUserAll"}, {"method": "post", "path": "/userUpdate"}, {"method": "post", "path": "/homeCreate"}, {"method": "post", "path": "/douyinHome"}, {"method": "post", "path": "/tagCreate"}, {"method": "post", "path": "/tagUpdate"}, {"method": "post", "path": "/tagDelete"}, {"method": "post", "path": "/urlCreate"}, {"method": "post", "path": "/urlUpdate"}, {"method": "post", "path": "/urlImages"}, {"method": "post", "path": "/urlDelete"}, {"method": "get", "path": "/qrCodeUrl"}, {"method": "post", "path": "/userPage"}, {"method": "post", "path": "/tagPage"}, {"method": "post", "path": "/urlPage"}, {"method": "post", "path": "/urlRedo"}, {"method": "get", "path": "/account"}, {"method": "get", "path": "/info"}]}, {"prefix": "/admin/base/common/event", "name": "TblEvent", "api": [{"method": "post", "path": "/updateTotalRedirectCount"}, {"method": "post", "path": "/updateTotalDisplayCount"}, {"method": "post", "path": "/push1WwwPositionUpdate"}, {"method": "post", "path": "/schoolCreateAndUpdate"}, {"method": "post", "path": "/userCreateAndUpdate"}, {"method": "post", "path": "/coreLocationDelete"}, {"method": "post", "path": "/pushLocationDelete"}, {"method": "post", "path": "/push1CountUpdate"}, {"method": "post", "path": "/push1WwwPosition"}, {"method": "post", "path": "/push2ComingSoon"}, {"method": "post", "path": "/calendarEditOne"}, {"method": "post", "path": "/publishToApply"}, {"method": "post", "path": "/releaseEditOne"}, {"method": "post", "path": "/calendarDelete"}, {"method": "post", "path": "/materialDigest"}, {"method": "post", "path": "/publishToIESE"}, {"method": "post", "path": "/majorCategory"}, {"method": "post", "path": "/releaseDelete"}, {"method": "post", "path": "/getReleaseOne"}, {"method": "post", "path": "/releaseCreate"}, {"method": "post", "path": "/externalsLog"}, {"method": "post", "path": "/wwwHotUpdate"}, {"method": "post", "path": "/calendarList"}, {"method": "post", "path": "/calendarEdit"}, {"method": "post", "path": "/calendarInfo"}, {"method": "post", "path": "/school_major"}, {"method": "post", "path": "/changeOrder"}, {"method": "post", "path": "/releaseList"}, {"method": "post", "path": "/release1To3"}, {"method": "post", "path": "/releaseEdit"}, {"method": "post", "path": "/getCalendar"}, {"method": "post", "path": "/imageDelete"}, {"method": "post", "path": "/imageDigest"}, {"method": "post", "path": "/ieseUpdate"}, {"method": "post", "path": "/ieseDelete"}, {"method": "post", "path": "/schoolList"}, {"method": "post", "path": "/userDelete"}, {"method": "post", "path": "/push1Count"}, {"method": "post", "path": "/externals"}, {"method": "post", "path": "/ieseList"}, {"method": "post", "path": "/acUpdate"}, {"method": "post", "path": "/userList"}, {"method": "post", "path": "/push1Add"}, {"method": "post", "path": "/calendar"}, {"method": "post", "path": "/ieseAdd"}, {"method": "post", "path": "/offline"}, {"method": "post", "path": "/urlPage"}, {"method": "post", "path": "/acList"}, {"method": "post", "path": "/wwwHot"}, {"method": "post", "path": "/getGeo"}, {"method": "post", "path": "/remove"}, {"method": "post", "path": "/create"}, {"method": "post", "path": "/core"}, {"method": "post", "path": "/edit"}, {"method": "post", "path": "/type"}, {"method": "post", "path": "/orgs"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/geo"}]}, {"prefix": "/admin/base/common/forum", "name": "CcForumThread", "api": [{"method": "get", "path": "/exportSensitiveContentToExcel"}, {"method": "post", "path": "/searchRecommendGroupUpdate"}, {"method": "post", "path": "/searchRecommendGroupDelete"}, {"method": "get", "path": "/initSearchRecommandIndex"}, {"method": "post", "path": "/searchRecommendGroupList"}, {"method": "post", "path": "/searchRecommendGroupPage"}, {"method": "post", "path": "/searchRecommendGroupAdd"}, {"method": "post", "path": "/sensitiveMonitorUpdate"}, {"method": "post", "path": "/searchRecommendUpdate"}, {"method": "post", "path": "/searchRecommendDelete"}, {"method": "post", "path": "/searchRecommendPage"}, {"method": "post", "path": "/sensitiveMonitorGet"}, {"method": "get", "path": "/initForumIndexData"}, {"method": "post", "path": "/searchRecommendAdd"}, {"method": "post", "path": "/threadWeightDelete"}, {"method": "post", "path": "/getThreadUrlByPid"}, {"method": "post", "path": "/updateShowMobile"}, {"method": "post", "path": "/appFeatureSwitch"}, {"method": "post", "path": "/threadWeightPage"}, {"method": "get", "path": "/fixForumPostData"}, {"method": "post", "path": "/highlightDigest"}, {"method": "post", "path": "/threadWeightAdd"}, {"method": "post", "path": "/publishToForum"}, {"method": "get", "path": "/initForumIndex"}, {"method": "post", "path": "/getShowMobile"}, {"method": "post", "path": "/publishToWWW"}, {"method": "post", "path": "/threadWeight"}, {"method": "post", "path": "/sensitive"}, {"method": "post", "path": "/subnav"}, {"method": "post", "path": "/stick"}, {"method": "get", "path": "/info"}, {"method": "get", "path": "/nav"}]}, {"prefix": "/admin/base/common/go", "name": "Url", "api": [{"method": "post", "path": "/urlStatisticsDel"}, {"method": "post", "path": "/statisticByUrl"}, {"method": "post", "path": "/detailByUrl"}, {"method": "post", "path": "/queryGroup"}, {"method": "post", "path": "/urlUpdate"}, {"method": "post", "path": "/urlDelete"}, {"method": "get", "path": "/urlPage"}, {"method": "post", "path": "/urlByID"}, {"method": "post", "path": "/urlSub"}, {"method": "post", "path": "/urlAdd"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/export"}, {"method": "get", "path": "/info"}]}, {"prefix": "/admin/base/common/hotspot", "name": "CommonHotspotEntity", "api": [{"method": "post", "path": "/hotspotCreate"}, {"method": "post", "path": "/hotspotUpdate"}, {"method": "post", "path": "/hotspotDelete"}, {"method": "post", "path": "/hotSpotPage"}, {"method": "post", "path": "/hotspotAdd"}, {"method": "post", "path": "/update"}]}, {"prefix": "/admin/base/common/iese", "name": "BlogEntity", "api": [{"method": "post", "path": "/findOne"}, {"method": "post", "path": "/create"}, {"method": "post", "path": "/pages"}, {"method": "post", "path": "/edit"}, {"method": "post", "path": "/tags"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/del"}]}, {"prefix": "/admin/base/common/portal", "name": "PortalArticleEntity", "api": [{"method": "post", "path": "/candidateArticleFindOne"}, {"method": "post", "path": "/candidateArticleCreate"}, {"method": "post", "path": "/candidateArticleUpdate"}, {"method": "post", "path": "/candidateArticleDelete"}, {"method": "post", "path": "/waitingArticleFindOne"}, {"method": "get", "path": "/getPortalBannerIndex"}, {"method": "post", "path": "/setPortalBannerIndex"}, {"method": "post", "path": "/candidateArticlePage"}, {"method": "post", "path": "/waitingArticleCreate"}, {"method": "post", "path": "/waitingArticleUpdate"}, {"method": "post", "path": "/waitingArticleDelete"}, {"method": "post", "path": "/updateDisplayOrder"}, {"method": "post", "path": "/waitingArticlePage"}, {"method": "post", "path": "/getAvailableTags"}, {"method": "post", "path": "/portalFindOne"}, {"method": "post", "path": "/portalCreate"}, {"method": "post", "path": "/portalUpdate"}, {"method": "post", "path": "/portalDelete"}, {"method": "post", "path": "/portalPage"}, {"method": "post", "path": "/tagSearch"}, {"method": "post", "path": "/update"}]}, {"prefix": "/admin/base/common/searchLog", "name": "CcCommonSearchLog", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}]}, {"prefix": "/admin/base/common/sougou", "name": "CommonSougouEntity", "api": [{"method": "get", "path": "/initIndex"}, {"method": "post", "path": "/initData"}, {"method": "post", "path": "/create"}, {"method": "post", "path": "/search"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/more"}]}, {"prefix": "/admin/base/common/wechat", "name": "WechatGroupEntity", "api": [{"method": "post", "path": "/updateGroup"}, {"method": "post", "path": "/wechatInfo"}, {"method": "post", "path": "/getQrCode"}, {"method": "post", "path": "/messages"}, {"method": "post", "path": "/logout"}, {"method": "post", "path": "/list"}, {"method": "get", "path": "/info"}]}, {"prefix": "/admin/base/open", "name": "", "api": [{"method": "post", "path": "/upload4EventSchoolLogo"}, {"method": "get", "path": "/push1_www_position"}, {"method": "post", "path": "/upload4EventPush2"}, {"method": "post", "path": "/upload4PortalPic"}, {"method": "post", "path": "/appFeatureSwitch"}, {"method": "get", "path": "/checkForumLogin"}, {"method": "get", "path": "/searchRecommend"}, {"method": "post", "path": "/upload4IESEPic"}, {"method": "post", "path": "/upload4Wechat"}, {"method": "get", "path": "/refreshToken"}, {"method": "post", "path": "/upload4Event"}, {"method": "get", "path": "/showCalendar"}, {"method": "get", "path": "/searchItems"}, {"method": "get", "path": "/showRelease"}, {"method": "get", "path": "/ieseEvents"}, {"method": "post", "path": "/upload4GPT"}, {"method": "get", "path": "/ieseBlogs"}, {"method": "post", "path": "/sendmail"}, {"method": "post", "path": "/gptsAuth"}, {"method": "get", "path": "/captcha"}, {"method": "post", "path": "/hotspot"}, {"method": "get", "path": "/www_hot"}, {"method": "post", "path": "/search"}, {"method": "get", "path": "/wwwCDG"}, {"method": "post", "path": "/login"}, {"method": "get", "path": "/wecom"}, {"method": "post", "path": "/wecom"}, {"method": "get", "path": "/html"}, {"method": "get", "path": "/eps"}, {"method": "post", "path": "/ttt"}, {"method": "get", "path": "/previewGptImg/:img"}, {"method": "get", "path": "/previewGptMp3/:file"}, {"method": "get", "path": "/eventShow/:id"}, {"method": "get", "path": "/blogShow/:id"}]}, {"prefix": "/admin/base/sys/department", "name": "BaseSysDepartmentEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/order"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/add"}]}, {"prefix": "/admin/base/sys/log", "name": "BaseSysLogEntity", "api": [{"method": "post", "path": "/setKeep"}, {"method": "get", "path": "/getKeep"}, {"method": "post", "path": "/clear"}, {"method": "post", "path": "/page"}]}, {"prefix": "/admin/base/sys/menu", "name": "BaseSysMenuEntity", "api": [{"method": "post", "path": "/create"}, {"method": "post", "path": "/export"}, {"method": "post", "path": "/import"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/parse"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}]}, {"prefix": "/admin/base/sys/param", "name": "BaseSysParamEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/html"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}]}, {"prefix": "/admin/base/sys/role", "name": "BaseSysRoleEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}]}, {"prefix": "/admin/base/sys/user", "name": "BaseSysUserEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/move"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}]}, {"prefix": "/admin/dict/info", "name": "DictInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/data"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}]}, {"prefix": "/admin/dict/type", "name": "DictTypeEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}]}, {"prefix": "/admin/plugin/info", "name": "PluginInfoEntity", "api": [{"method": "post", "path": "/install"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}]}, {"prefix": "/admin/recycle/data", "name": "RecycleDataEntity", "api": [{"method": "post", "path": "/restore"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/page"}]}, {"prefix": "/admin/space/info", "name": "SpaceInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}]}, {"prefix": "/admin/space/type", "name": "SpaceTypeEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}]}, {"prefix": "/admin/task/info", "name": "TaskInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/start"}, {"method": "post", "path": "/once"}, {"method": "post", "path": "/stop"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/page"}, {"method": "get", "path": "/log"}, {"method": "post", "path": "/add"}]}, {"prefix": "/admin/user/address", "name": "UserAddressEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}]}, {"prefix": "/admin/user/info", "name": "UserInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}]}]